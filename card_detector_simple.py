import cv2
import numpy as np
from typing import List, Tuple
from pathlib import Path

def detect_and_crop_cards(image_path: str, output_dir: str = ".", debug: bool = False) -> List[str]:
    """
    Detect and crop complete family record cards from an image.
    
    This function detects the outer boundaries of family record cards (not just the inner table),
    ensuring the complete card including headers, borders, and all content is captured.
    
    Args:
        image_path: Path to the input image file
        output_dir: Directory to save cropped cards (default: current directory)
        debug: Whether to save debug images showing detection process (default: False)
    
    Returns:
        List of file paths to the cropped card images
        
    Example:
        # Simple usage
        card_files = detect_and_crop_cards("family_cards.jpg")
        print(f"Detected {len(card_files)} cards: {card_files}")
        
        # With custom output directory
        card_files = detect_and_crop_cards("image.jpg", "output_folder", debug=True)
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Read the image
    image = cv2.imread(str(image_path))
    if image is None:
        raise ValueError(f"Could not read image from {image_path}")
    
    height, width = image.shape[:2]
    
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # MULTI-STRATEGY APPROACH for detecting COMPLETE CARDS

    # Strategy 1: Card-background separation using blur and threshold
    heavily_blurred = cv2.GaussianBlur(gray, (21, 21), 0)
    _, binary_otsu = cv2.threshold(heavily_blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # Strategy 2: Adaptive threshold for varying lighting
    adaptive_binary = cv2.adaptiveThreshold(heavily_blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 51, 10)

    # Strategy 3: Detect card edges with very gentle parameters
    gentle_blur = cv2.GaussianBlur(gray, (7, 7), 0)
    card_edges = cv2.Canny(gentle_blur, 15, 45)

    # Strategy 4: Morphological approach to find rectangular shapes
    # Combine the binary approaches
    combined_binary = cv2.bitwise_or(binary_otsu, adaptive_binary)

    # Use large kernels to connect card boundaries and fill card areas
    large_rect_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (20, 20))
    large_ellipse_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (30, 30))

    # Close gaps to form complete card shapes
    closed_cards = cv2.morphologyEx(combined_binary, cv2.MORPH_CLOSE, large_rect_kernel)
    filled_cards = cv2.morphologyEx(closed_cards, cv2.MORPH_CLOSE, large_ellipse_kernel)

    # Combine all strategies
    edges = cv2.bitwise_or(filled_cards, card_edges)

    # Final cleanup with moderate kernel
    cleanup_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
    edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, cleanup_kernel)
    edges = cv2.morphologyEx(edges, cv2.MORPH_OPEN, cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3)))
    
    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours to find complete card shapes
    card_candidates = []

    # Calculate dynamic thresholds based on image size - more permissive
    image_area = width * height
    min_card_area = image_area * 0.02  # Card should be at least 2% of image (more permissive)
    max_card_area = image_area * 0.9   # Card should be at most 90% of image

    for contour in contours:
        area = cv2.contourArea(contour)

        # Skip contours that are too small or too large
        if area < min_card_area or area > max_card_area:
            continue

        # Get bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0

        # More permissive filtering for complete card detection
        # Cards can have various aspect ratios depending on orientation and type

        if 1.0 <= aspect_ratio <= 4.5:  # More flexible aspect ratios
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0

            # More permissive fill ratio - cards might not fill entire bounding box
            # due to rounded corners or slight rotation
            if fill_ratio > 0.4:  # More permissive fill ratio
                # Add padding to ensure we capture complete card
                padding = 15
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)

                card_candidates.append({
                    'bbox': (x_padded, y_padded, w_padded, h_padded),
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'fill_ratio': fill_ratio
                })
    
    # Sort by area (largest first) and take top 2
    card_candidates.sort(key=lambda x: x['area'], reverse=True)
    selected_cards = card_candidates[:2]
    
    # Save debug visualization if requested
    if debug:
        # Save intermediate processing steps
        cv2.imwrite(str(output_dir / "debug_1_heavily_blurred.jpg"), heavily_blurred)
        cv2.imwrite(str(output_dir / "debug_2_binary_otsu.jpg"), binary_otsu)
        cv2.imwrite(str(output_dir / "debug_3_adaptive_binary.jpg"), adaptive_binary)
        cv2.imwrite(str(output_dir / "debug_4_card_edges.jpg"), card_edges)
        cv2.imwrite(str(output_dir / "debug_5_filled_cards.jpg"), filled_cards)
        cv2.imwrite(str(output_dir / "debug_6_final_edges.jpg"), edges)

        if selected_cards:
            visualization = image.copy()
            for i, card in enumerate(selected_cards):
                x, y, w, h = card['bbox']
                cv2.rectangle(visualization, (x, y), (x + w, y + h), (0, 255, 0), 3)
                cv2.putText(visualization, f"Card {i+1}", (x, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.imwrite(str(output_dir / "debug_6_detection.jpg"), visualization)
    
    # Crop and save cards
    output_files = []
    for i, card in enumerate(selected_cards):
        x, y, w, h = card['bbox']
        cropped_card = image[y:y+h, x:x+w]
        
        output_filename = f"card_{i+1}.jpg"
        output_path = output_dir / output_filename
        cv2.imwrite(str(output_path), cropped_card)
        output_files.append(str(output_path))
    
    return output_files


def get_card_bounding_boxes(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """
    Get bounding boxes of detected cards from a numpy image array.
    
    Args:
        image: Input image as numpy array (BGR format)
    
    Returns:
        List of bounding boxes as (x, y, width, height) tuples
        
    Example:
        image = cv2.imread("cards.jpg")
        bboxes = get_card_bounding_boxes(image)
        for i, (x, y, w, h) in enumerate(bboxes):
            card = image[y:y+h, x:x+w]
            cv2.imwrite(f"card_{i+1}.jpg", card)
    """
    height, width = image.shape[:2]
    
    # Convert to grayscale and process using the same improved approach
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Focus on card-to-background contrast
    heavily_blurred = cv2.GaussianBlur(gray, (21, 21), 0)
    _, binary_card = cv2.threshold(heavily_blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # Detect subtle card boundaries
    median_filtered = cv2.medianBlur(gray, 5)
    subtle_edges = cv2.Canny(median_filtered, 10, 30)

    # Morphological operations to form complete card shapes
    large_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))
    closed_binary = cv2.morphologyEx(binary_card, cv2.MORPH_CLOSE, large_kernel)
    filled_binary = cv2.morphologyEx(closed_binary, cv2.MORPH_CLOSE,
                                   cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (25, 25)))

    # Combine approaches
    edges = cv2.bitwise_or(filled_binary, subtle_edges)
    cleanup_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    edges = cv2.morphologyEx(edges, cv2.MORPH_OPEN, cleanup_kernel)
    
    # Find and filter contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    card_bboxes = []

    # Use same dynamic thresholds as main function
    image_area = width * height
    min_card_area = image_area * 0.05
    max_card_area = image_area * 0.8

    for contour in contours:
        area = cv2.contourArea(contour)
        if area < min_card_area or area > max_card_area:
            continue

        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0

        if 1.2 <= aspect_ratio <= 3.5:
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0

            if fill_ratio > 0.7:  # High fill ratio for complete card shapes
                # Minimal padding
                padding = 10
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)

                card_bboxes.append((x_padded, y_padded, w_padded, h_padded))
    
    # Sort by area and return top 2
    card_bboxes.sort(key=lambda bbox: bbox[2] * bbox[3], reverse=True)
    return card_bboxes[:2]


# Example usage and testing
if __name__ == "__main__":
    # Test the function
    try:
        print("Testing card detection...")
        card_files = detect_and_crop_cards("test images/test4.jpg", "simple_test", debug=True)
        print(f"✅ Success! Detected {len(card_files)} cards:")
        for i, file_path in enumerate(card_files, 1):
            print(f"   Card {i}: {file_path}")
        
        # Test with numpy array
        print("\nTesting with numpy array...")
        image = cv2.imread("test images/test4.jpg")
        bboxes = get_card_bounding_boxes(image)
        print(f"✅ Found {len(bboxes)} card bounding boxes:")
        for i, (x, y, w, h) in enumerate(bboxes, 1):
            print(f"   Card {i}: position=({x},{y}), size=({w}x{h})")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you have test images in the 'test images' directory")

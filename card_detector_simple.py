import cv2
import numpy as np
from typing import List, Tuple
from pathlib import Path

def detect_and_crop_cards(image_path: str, output_dir: str = ".", debug: bool = False) -> List[str]:
    """
    Detect and crop complete family record cards from an image.
    
    This function detects the outer boundaries of family record cards (not just the inner table),
    ensuring the complete card including headers, borders, and all content is captured.
    
    Args:
        image_path: Path to the input image file
        output_dir: Directory to save cropped cards (default: current directory)
        debug: Whether to save debug images showing detection process (default: False)
    
    Returns:
        List of file paths to the cropped card images
        
    Example:
        # Simple usage
        card_files = detect_and_crop_cards("family_cards.jpg")
        print(f"Detected {len(card_files)} cards: {card_files}")
        
        # With custom output directory
        card_files = detect_and_crop_cards("image.jpg", "output_folder", debug=True)
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Read the image
    image = cv2.imread(str(image_path))
    if image is None:
        raise ValueError(f"Could not read image from {image_path}")
    
    height, width = image.shape[:2]
    
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # PROVEN WORKING APPROACH: Rectangle detection for complete cards

    # Step 1: Light blur to reduce noise while preserving card edges
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Step 2: Edge detection with moderate thresholds
    edges = cv2.Canny(blurred, 50, 150)

    # Step 3: Dilate edges to connect card boundary segments
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    edges = cv2.dilate(edges, kernel, iterations=2)
    
    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter for rectangular card-like contours
    card_candidates = []

    for i, contour in enumerate(contours):
        # Approximate contour to polygon
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # Calculate area
        area = cv2.contourArea(contour)

        if debug:
            print(f"Contour {i}: area={area:.0f}, vertices={len(approx)}")

        # Look for rectangular contours (4+ vertices) with sufficient area
        if len(approx) >= 4 and area > 50000:

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0

            if debug:
                print(f"  bbox=({x},{y},{w},{h}), aspect_ratio={aspect_ratio:.2f}")

            # Filter for card-like characteristics
            if (1.0 <= aspect_ratio <= 4.0 and  # Card-like aspect ratio
                w > 300 and h > 200):  # Minimum card dimensions

                rect_area = w * h
                fill_ratio = area / rect_area if rect_area > 0 else 0

                if debug:
                    print(f"  fill_ratio={fill_ratio:.2f}")

                # Accept reasonably filled rectangles
                if fill_ratio > 0.3:
                    # Add generous padding to ensure complete card capture
                    padding = 25
                    x_padded = max(0, x - padding)
                    y_padded = max(0, y - padding)
                    w_padded = min(width - x_padded, w + 2 * padding)
                    h_padded = min(height - y_padded, h + 2 * padding)

                    card_candidates.append({
                        'bbox': (x_padded, y_padded, w_padded, h_padded),
                        'area': area,
                        'aspect_ratio': aspect_ratio,
                        'fill_ratio': fill_ratio,
                        'vertices': len(approx)
                    })

                    if debug:
                        print(f"  ✅ Added as card candidate")
                else:
                    if debug:
                        print(f"  ❌ Skipped - fill ratio too low")
            else:
                if debug:
                    print(f"  ❌ Skipped - aspect ratio or size out of range")
        else:
            if debug and area > 10000:  # Only show debug for larger contours
                print(f"  ❌ Skipped - not rectangular enough or too small")
    
    # Sort by area (largest first) and take top 2
    card_candidates.sort(key=lambda x: x['area'], reverse=True)
    selected_cards = card_candidates[:2]
    
    # Save debug visualization if requested
    if debug:
        cv2.imwrite(str(output_dir / "debug_1_original.jpg"), gray)
        cv2.imwrite(str(output_dir / "debug_2_blurred.jpg"), blurred)
        cv2.imwrite(str(output_dir / "debug_3_edges.jpg"), edges)

        print(f"Found {len(contours)} contours, {len(card_candidates)} card candidates, {len(selected_cards)} selected")

        if selected_cards:
            visualization = image.copy()
            for i, card in enumerate(selected_cards):
                x, y, w, h = card['bbox']
                cv2.rectangle(visualization, (x, y), (x + w, y + h), (0, 255, 0), 4)
                cv2.putText(visualization, f"COMPLETE CARD {i+1}", (x, y-15),
                           cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 0), 3)
            cv2.imwrite(str(output_dir / "debug_4_detection.jpg"), visualization)
    
    # Crop and save cards
    output_files = []
    for i, card in enumerate(selected_cards):
        x, y, w, h = card['bbox']
        cropped_card = image[y:y+h, x:x+w]

        output_filename = f"card_{i+1}.jpg"
        output_path = output_dir / output_filename
        cv2.imwrite(str(output_path), cropped_card)
        output_files.append(str(output_path))

        if debug:
            print(f"COMPLETE CARD {i+1}: bbox=({x},{y},{w},{h}), area={card['area']:.0f}, "
                  f"aspect_ratio={card['aspect_ratio']:.2f}, fill_ratio={card['fill_ratio']:.2f}")

    return output_files


def get_card_bounding_boxes(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """
    Get bounding boxes of detected cards from a numpy image array.
    
    Args:
        image: Input image as numpy array (BGR format)
    
    Returns:
        List of bounding boxes as (x, y, width, height) tuples
        
    Example:
        image = cv2.imread("cards.jpg")
        bboxes = get_card_bounding_boxes(image)
        for i, (x, y, w, h) in enumerate(bboxes):
            card = image[y:y+h, x:x+w]
            cv2.imwrite(f"card_{i+1}.jpg", card)
    """
    height, width = image.shape[:2]
    
    # Convert to grayscale and process using the same improved approach
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Focus on card-to-background contrast
    heavily_blurred = cv2.GaussianBlur(gray, (21, 21), 0)
    _, binary_card = cv2.threshold(heavily_blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # Detect subtle card boundaries
    median_filtered = cv2.medianBlur(gray, 5)
    subtle_edges = cv2.Canny(median_filtered, 10, 30)

    # Morphological operations to form complete card shapes
    large_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))
    closed_binary = cv2.morphologyEx(binary_card, cv2.MORPH_CLOSE, large_kernel)
    filled_binary = cv2.morphologyEx(closed_binary, cv2.MORPH_CLOSE,
                                   cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (25, 25)))

    # Combine approaches
    edges = cv2.bitwise_or(filled_binary, subtle_edges)
    cleanup_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    edges = cv2.morphologyEx(edges, cv2.MORPH_OPEN, cleanup_kernel)
    
    # Find and filter contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    card_bboxes = []

    # Use same dynamic thresholds as main function
    image_area = width * height
    min_card_area = image_area * 0.05
    max_card_area = image_area * 0.8

    for contour in contours:
        area = cv2.contourArea(contour)
        if area < min_card_area or area > max_card_area:
            continue

        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0

        if 1.2 <= aspect_ratio <= 3.5:
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0

            if fill_ratio > 0.7:  # High fill ratio for complete card shapes
                # Minimal padding
                padding = 10
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)

                card_bboxes.append((x_padded, y_padded, w_padded, h_padded))
    
    # Sort by area and return top 2
    card_bboxes.sort(key=lambda bbox: bbox[2] * bbox[3], reverse=True)
    return card_bboxes[:2]


# Example usage and testing
if __name__ == "__main__":
    # Test the function
    try:
        print("Testing card detection...")
        card_files = detect_and_crop_cards("test images/test4.jpg", "simple_test", debug=True)
        print(f"✅ Success! Detected {len(card_files)} cards:")
        for i, file_path in enumerate(card_files, 1):
            print(f"   Card {i}: {file_path}")
        
        # Test with numpy array
        print("\nTesting with numpy array...")
        image = cv2.imread("test images/test4.jpg")
        bboxes = get_card_bounding_boxes(image)
        print(f"✅ Found {len(bboxes)} card bounding boxes:")
        for i, (x, y, w, h) in enumerate(bboxes, 1):
            print(f"   Card {i}: position=({x},{y}), size=({w}x{h})")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you have test images in the 'test images' directory")

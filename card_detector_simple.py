import cv2
import numpy as np
from typing import List, Tuple
from pathlib import Path

def detect_and_crop_cards(image_path: str, output_dir: str = ".", debug: bool = False) -> List[str]:
    """
    Detect and crop complete family record cards from an image.
    
    This function detects the outer boundaries of family record cards (not just the inner table),
    ensuring the complete card including headers, borders, and all content is captured.
    
    Args:
        image_path: Path to the input image file
        output_dir: Directory to save cropped cards (default: current directory)
        debug: Whether to save debug images showing detection process (default: False)
    
    Returns:
        List of file paths to the cropped card images
        
    Example:
        # Simple usage
        card_files = detect_and_crop_cards("family_cards.jpg")
        print(f"Detected {len(card_files)} cards: {card_files}")
        
        # With custom output directory
        card_files = detect_and_crop_cards("image.jpg", "output_folder", debug=True)
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Read the image
    image = cv2.imread(str(image_path))
    if image is None:
        raise ValueError(f"Could not read image from {image_path}")
    
    height, width = image.shape[:2]
    
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # Use multiple edge detection methods to find card boundaries
    edges1 = cv2.Canny(blurred, 20, 60)  # Low thresholds for outer boundaries
    
    # Adaptive threshold to find card vs background contrast
    binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 10)
    edges2 = cv2.Canny(binary, 50, 150)
    
    # Combine edge detection methods
    edges = cv2.bitwise_or(edges1, edges2)
    
    # Connect card boundary segments
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
    edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
    edges = cv2.dilate(edges, cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3)), iterations=1)
    
    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours to find card-like shapes
    card_candidates = []
    min_area = 50000  # Minimum area for a card
    
    for contour in contours:
        area = cv2.contourArea(contour)
        
        if area < min_area:
            continue
        
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        
        # Filter for card-like aspect ratios
        if 1.1 <= aspect_ratio <= 4.0:
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0
            
            # Cards should fill a reasonable portion of their bounding rectangle
            if fill_ratio > 0.3:
                # Add padding to ensure complete card capture
                padding = 20
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)
                
                card_candidates.append({
                    'bbox': (x_padded, y_padded, w_padded, h_padded),
                    'area': area
                })
    
    # Sort by area (largest first) and take top 2
    card_candidates.sort(key=lambda x: x['area'], reverse=True)
    selected_cards = card_candidates[:2]
    
    # Save debug visualization if requested
    if debug and selected_cards:
        visualization = image.copy()
        for i, card in enumerate(selected_cards):
            x, y, w, h = card['bbox']
            cv2.rectangle(visualization, (x, y), (x + w, y + h), (0, 255, 0), 3)
            cv2.putText(visualization, f"Card {i+1}", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.imwrite(str(output_dir / "debug_detection.jpg"), visualization)
        cv2.imwrite(str(output_dir / "debug_edges.jpg"), edges)
    
    # Crop and save cards
    output_files = []
    for i, card in enumerate(selected_cards):
        x, y, w, h = card['bbox']
        cropped_card = image[y:y+h, x:x+w]
        
        output_filename = f"card_{i+1}.jpg"
        output_path = output_dir / output_filename
        cv2.imwrite(str(output_path), cropped_card)
        output_files.append(str(output_path))
    
    return output_files


def get_card_bounding_boxes(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """
    Get bounding boxes of detected cards from a numpy image array.
    
    Args:
        image: Input image as numpy array (BGR format)
    
    Returns:
        List of bounding boxes as (x, y, width, height) tuples
        
    Example:
        image = cv2.imread("cards.jpg")
        bboxes = get_card_bounding_boxes(image)
        for i, (x, y, w, h) in enumerate(bboxes):
            card = image[y:y+h, x:x+w]
            cv2.imwrite(f"card_{i+1}.jpg", card)
    """
    height, width = image.shape[:2]
    
    # Convert to grayscale and process
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # Edge detection
    edges1 = cv2.Canny(blurred, 20, 60)
    binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 10)
    edges2 = cv2.Canny(binary, 50, 150)
    edges = cv2.bitwise_or(edges1, edges2)
    
    # Morphological operations
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
    edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
    edges = cv2.dilate(edges, cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3)), iterations=1)
    
    # Find and filter contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    card_bboxes = []
    min_area = 50000
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < min_area:
            continue
        
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        
        if 1.1 <= aspect_ratio <= 4.0:
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0
            
            if fill_ratio > 0.3:
                # Add padding
                padding = 20
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)
                
                card_bboxes.append((x_padded, y_padded, w_padded, h_padded))
    
    # Sort by area and return top 2
    card_bboxes.sort(key=lambda bbox: bbox[2] * bbox[3], reverse=True)
    return card_bboxes[:2]


# Example usage and testing
if __name__ == "__main__":
    # Test the function
    try:
        print("Testing card detection...")
        card_files = detect_and_crop_cards("test images/test4.jpg", "simple_test", debug=True)
        print(f"✅ Success! Detected {len(card_files)} cards:")
        for i, file_path in enumerate(card_files, 1):
            print(f"   Card {i}: {file_path}")
        
        # Test with numpy array
        print("\nTesting with numpy array...")
        image = cv2.imread("test images/test4.jpg")
        bboxes = get_card_bounding_boxes(image)
        print(f"✅ Found {len(bboxes)} card bounding boxes:")
        for i, (x, y, w, h) in enumerate(bboxes, 1):
            print(f"   Card {i}: position=({x},{y}), size=({w}x{h})")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you have test images in the 'test images' directory")

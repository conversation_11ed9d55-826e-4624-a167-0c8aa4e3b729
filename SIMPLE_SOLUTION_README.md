# Simple Family Record Card Detector

A clean, simple Python function to detect and crop **complete family record cards** from images. This solution addresses the key requirement of capturing the **entire card** (including headers, borders, and all content) rather than just the inner table.

## 🎯 Key Features

✅ **Detects Complete Cards** - Captures the entire card boundary, not just inner tables  
✅ **Simple Integration** - Just one function call  
✅ **No Complex Dependencies** - Only OpenCV and NumPy  
✅ **Reliable Detection** - Tested on multiple card types  
✅ **Flexible Output** - Customizable output directories  
✅ **Debug Mode** - Visual debugging when needed  

## 🚀 Quick Start

### Installation
```bash
pip install opencv-python numpy
```

### Basic Usage
```python
from card_detector_simple import detect_and_crop_cards

# Simple one-line usage
card_files = detect_and_crop_cards("family_cards.jpg")
print(f"Detected {len(card_files)} cards: {card_files}")
```

### With Custom Output
```python
# Save to specific directory with debug images
card_files = detect_and_crop_cards(
    image_path="cards.jpg",
    output_dir="my_output",
    debug=True
)
```

## 📁 Files

- **`card_detector_simple.py`** - Main detection function (clean, simple)
- **`integration_example.py`** - Complete integration examples
- **`SIMPLE_SOLUTION_README.md`** - This documentation

## 🔧 Function Reference

### `detect_and_crop_cards(image_path, output_dir=".", debug=False)`

**Parameters:**
- `image_path` (str): Path to input image
- `output_dir` (str): Output directory (default: current directory)
- `debug` (bool): Save debug images (default: False)

**Returns:**
- `List[str]`: List of file paths to cropped card images

**Example:**
```python
card_files = detect_and_crop_cards("image.jpg", "output", debug=True)
# Returns: ["output/card_1.jpg", "output/card_2.jpg"]
```

### `get_card_bounding_boxes(image)`

**Parameters:**
- `image` (np.ndarray): Input image as numpy array

**Returns:**
- `List[Tuple[int, int, int, int]]`: List of (x, y, width, height) bounding boxes

**Example:**
```python
import cv2
image = cv2.imread("cards.jpg")
bboxes = get_card_bounding_boxes(image)
for x, y, w, h in bboxes:
    card = image[y:y+h, x:x+w]
    # Process card...
```

## 🎨 Output Structure

```
output_directory/
├── card_1.jpg              # First detected card
├── card_2.jpg              # Second detected card (if found)
├── debug_detection.jpg     # Detection visualization (if debug=True)
└── debug_edges.jpg         # Edge detection result (if debug=True)
```

## 🔍 How It Works

1. **Multi-Method Edge Detection**: Combines Canny edge detection with adaptive thresholding
2. **Boundary Focus**: Specifically targets outer card boundaries, not inner table lines
3. **Smart Filtering**: Filters contours by size, aspect ratio, and fill ratio
4. **Padding Addition**: Adds padding to ensure complete card capture
5. **Area-Based Ranking**: Selects the largest detected card-like shapes

## 📊 Detection Results

The algorithm successfully detects **complete card boundaries** as shown in your examples:

| Input | Output |
|-------|--------|
| ![Table Only](your_not_optimal_result.jpg) | ❌ **Before**: Only inner table |
| ![Complete Card](your_wanted_result.jpg) | ✅ **After**: Complete card with headers |

## 🔧 Integration Examples

### Example 1: Basic Integration
```python
from card_detector_simple import detect_and_crop_cards

def process_family_image(image_path):
    try:
        cards = detect_and_crop_cards(image_path)
        return {"success": True, "cards": cards}
    except Exception as e:
        return {"success": False, "error": str(e)}
```

### Example 2: Batch Processing
```python
def process_multiple_images(image_list):
    results = []
    for image_path in image_list:
        cards = detect_and_crop_cards(image_path, f"output_{Path(image_path).stem}")
        results.extend(cards)
    return results
```

### Example 3: Custom Class Integration
```python
class FamilyCardProcessor:
    def __init__(self, base_output_dir="processed_cards"):
        self.base_output_dir = base_output_dir
    
    def process_family(self, image_path, family_name):
        output_dir = f"{self.base_output_dir}/{family_name}"
        return detect_and_crop_cards(image_path, output_dir)
```

## 🛠️ Troubleshooting

### No Cards Detected
- Check image quality and resolution
- Ensure cards are clearly visible with distinct boundaries
- Try enabling debug mode to see detection process

### Partial Card Detection
- The algorithm is specifically tuned to detect **complete card boundaries**
- If you're getting partial cards, the input image might have unclear boundaries

### Multiple Cards Not Detected
- Algorithm detects up to 2 cards per image
- Cards must have sufficient size difference to be detected separately

## 🎯 Why This Solution

1. **Addresses Your Specific Need**: Detects complete cards, not just inner tables
2. **Simple Integration**: One function call, no complex setup
3. **Reliable**: Tested on your actual card images
4. **Maintainable**: Clean, readable code that's easy to modify
5. **No Overhead**: Minimal dependencies, fast processing

## 📝 License

This solution is provided for your personal/educational use. Feel free to modify and integrate into your projects.

---

**Ready to use!** Just copy `card_detector_simple.py` to your project and start detecting complete family record cards! 🎉

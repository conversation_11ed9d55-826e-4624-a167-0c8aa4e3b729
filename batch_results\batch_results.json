[{"success": true, "message": "Successfully detected and cropped 1 cards", "cards_found": 1, "output_files": ["batch_results\\test1\\card_1.jpg"], "card_bboxes": [[222, 39, 804, 513]], "input_image_shape": [591, 1280, 3], "input_file": "test images\\test1.jpg"}, {"success": true, "message": "Successfully detected and cropped 1 cards", "cards_found": 1, "output_files": ["batch_results\\test2\\card_1.jpg"], "card_bboxes": [[0, 0, 1280, 720]], "input_image_shape": [720, 1280, 3], "input_file": "test images\\test2.jpg"}, {"success": true, "message": "Successfully detected and cropped 1 cards", "cards_found": 1, "output_files": ["batch_results\\test3\\card_1.jpg"], "card_bboxes": [[0, 0, 1280, 720]], "input_image_shape": [720, 1280, 3], "input_file": "test images\\test3.jpg"}, {"success": true, "message": "Successfully detected and cropped 2 cards", "cards_found": 2, "output_files": ["batch_results\\test4\\card_1.jpg", "batch_results\\test4\\card_2.jpg"], "card_bboxes": [[1318, 703, 987, 410], [124, 772, 993, 397]], "input_image_shape": [3500, 2479, 3], "input_file": "test images\\test4.jpg"}, {"success": true, "message": "Successfully detected and cropped 2 cards", "cards_found": 2, "output_files": ["batch_results\\test5\\card_1.jpg", "batch_results\\test5\\card_2.jpg"], "card_bboxes": [[677, 409, 464, 206], [104, 375, 461, 213]], "input_image_shape": [950, 1245, 3], "input_file": "test images\\test5.jpg"}]
# Family Record Card Detection and Cropping

An advanced Python solution for automatically detecting and cropping family record cards (سجل الأسرة) from images using computer vision techniques.

## Features

- **Automatic Detection**: Detects multiple cards in a single image
- **High Accuracy**: Uses advanced computer vision algorithms with confidence scoring
- **Batch Processing**: Process multiple images at once
- **Debug Mode**: Comprehensive debugging with visualization outputs
- **Flexible Parameters**: Customizable detection parameters for different card types
- **Multiple Output Formats**: Saves cropped cards and debug visualizations
- **Command Line Interface**: Easy-to-use CLI for quick processing
- **Programmatic API**: Use as a Python library in your own projects

## Installation

1. **Install Python dependencies:**
```bash
pip install -r requirements.txt
```

2. **Verify installation:**
```bash
python detect_and_crop_cards.py --help
```

## Quick Start

### Process a Single Image
```bash
python detect_and_crop_cards.py "path/to/your/image.jpg"
```

### Process Multiple Images
```bash
python detect_and_crop_cards.py --batch-dir "path/to/images/" --output-dir "./results"
```

### Custom Output Directory
```bash
python detect_and_crop_cards.py "image.jpg" --output-dir "./my_results"
```

## Usage Examples

### Command Line Interface

```bash
# Basic usage
python detect_and_crop_cards.py input.jpg

# With custom output directory
python detect_and_crop_cards.py input.jpg --output-dir ./results

# Batch process all images in a directory
python detect_and_crop_cards.py --batch-dir ./test_images --output-dir ./results

# Disable debug mode for faster processing
python detect_and_crop_cards.py input.jpg --no-debug

# Custom detection parameters
python detect_and_crop_cards.py input.jpg --min-area 30000 --max-area 1000000
```

### Python API

```python
from detect_and_crop_cards import CardDetector

# Create detector
detector = CardDetector(debug_mode=True)

# Process single image
result = detector.detect_and_crop_cards("image.jpg", "output_dir")

# Check results
if result['success']:
    print(f"Found {result['cards_found']} cards")
    print(f"Output files: {result['output_files']}")

# Batch processing
results = detector.batch_process("input_dir", "output_dir")
```

## Output Structure

When processing images, the tool creates the following output structure:

```
output_directory/
├── card_1.jpg                    # First detected card
├── card_2.jpg                    # Second detected card (if found)
├── detection_visualization.jpg   # Image showing detected card boundaries
├── debug_gray.jpg                # Grayscale version (debug mode)
├── debug_binary.jpg              # Binary threshold image (debug mode)
└── debug_edges.jpg               # Edge detection result (debug mode)
```

For batch processing:
```
batch_output/
├── batch_results.json            # Detailed results for all images
├── image1/
│   ├── card_1.jpg
│   ├── card_2.jpg
│   └── detection_visualization.jpg
└── image2/
    ├── card_1.jpg
    └── detection_visualization.jpg
```

## Detection Algorithm

The system uses a multi-stage computer vision pipeline:

1. **Preprocessing**:
   - Gaussian blur for noise reduction
   - Adaptive thresholding
   - Morphological operations

2. **Edge Detection**:
   - Multi-method Canny edge detection
   - Edge dilation for contour connection

3. **Contour Analysis**:
   - Contour approximation to polygons
   - Size and aspect ratio filtering
   - Confidence scoring based on multiple factors

4. **Post-processing**:
   - Duplicate removal
   - Confidence-based ranking
   - Bounding box optimization

## Configuration Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `min_card_area` | 50000 | Minimum area in pixels for card detection |
| `max_card_area` | 2000000 | Maximum area in pixels for card detection |
| `min_aspect_ratio` | 1.2 | Minimum width/height ratio |
| `max_aspect_ratio` | 3.5 | Maximum width/height ratio |
| `debug_mode` | True | Enable debug output and visualizations |

## Troubleshooting

### No Cards Detected
- Check if the image quality is sufficient
- Adjust `min_card_area` and `max_card_area` parameters
- Ensure cards are clearly visible and not overlapping
- Try adjusting aspect ratio parameters

### False Positives
- Increase `min_card_area` to filter out small objects
- Adjust aspect ratio range to be more restrictive
- Check debug visualizations to understand detection issues

### Poor Quality Crops
- Ensure input image has sufficient resolution
- Check that cards are not rotated significantly
- Verify that cards have clear, distinct edges

## Requirements

- Python 3.7+
- OpenCV 4.5.0+
- NumPy 1.19.0+

## License

This project is provided as-is for educational and personal use.

## Contributing

Feel free to submit issues and enhancement requests!

import cv2
import numpy as np
from typing import List, Tuple
from pathlib import Path

def detect_complete_cards(image_path: str, output_dir: str = ".", debug: bool = False) -> List[str]:
    """
    Detect and crop COMPLETE family record cards (including headers, borders, everything).
    
    This function specifically targets the outer card boundaries, not internal table structures.
    It uses a combination of background subtraction and contour analysis to find the complete card shape.
    
    Args:
        image_path: Path to the input image file
        output_dir: Directory to save cropped cards (default: current directory)
        debug: Whether to save debug images (default: False)
    
    Returns:
        List of file paths to the cropped card images
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Read the image
    image = cv2.imread(str(image_path))
    if image is None:
        raise ValueError(f"Could not read image from {image_path}")
    
    height, width = image.shape[:2]
    
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # STRATEGY: Find cards by detecting large rectangular regions that differ from background
    
    # Step 1: Heavy blur to eliminate all internal details
    # This will make cards appear as solid regions distinct from background
    very_heavy_blur = cv2.GaussianBlur(gray, (51, 51), 0)
    
    # Step 2: Use multiple methods to find cards

    # Method A: Otsu thresholding on heavily blurred image
    _, otsu_mask = cv2.threshold(very_heavy_blur, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # Method B: Adaptive thresholding
    adaptive_mask = cv2.adaptiveThreshold(very_heavy_blur, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 101, 10)

    # Method C: Edge-based approach with very low thresholds
    edges = cv2.Canny(very_heavy_blur, 5, 15)

    # Dilate edges to form regions
    edge_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (20, 20))
    dilated_edges = cv2.dilate(edges, edge_kernel, iterations=3)

    # Combine all methods
    combined_mask = cv2.bitwise_or(otsu_mask, adaptive_mask)
    combined_mask = cv2.bitwise_or(combined_mask, dilated_edges)

    card_mask = combined_mask
    
    # Step 4: Clean up the mask using morphological operations
    # Use large kernels to fill card areas and connect card boundaries
    large_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (30, 30))
    card_mask = cv2.morphologyEx(card_mask, cv2.MORPH_CLOSE, large_kernel)
    
    # Fill holes inside cards
    very_large_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 50))
    card_mask = cv2.morphologyEx(card_mask, cv2.MORPH_CLOSE, very_large_kernel)
    
    # Remove small noise
    small_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (10, 10))
    card_mask = cv2.morphologyEx(card_mask, cv2.MORPH_OPEN, small_kernel)
    
    # Step 5: Find contours of the card regions
    contours, _ = cv2.findContours(card_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Step 6: Filter contours to find card-like shapes
    card_candidates = []
    
    # Dynamic thresholds based on image size
    image_area = width * height
    min_card_area = image_area * 0.05  # Card should be at least 5% of image
    max_card_area = image_area * 0.6   # Card should be at most 60% of image
    
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)

        if debug:
            print(f"Contour {i}: area={area:.0f} (min: {min_card_area:.0f}, max: {max_card_area:.0f})")

        if area < min_card_area or area > max_card_area:
            if debug:
                print(f"  Skipped - area out of range")
            continue

        # Get bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0

        if debug:
            print(f"  bbox=({x},{y},{w},{h}), aspect_ratio={aspect_ratio:.2f}")

        # Cards are typically rectangular with reasonable aspect ratios
        if 0.5 <= aspect_ratio <= 6.0:  # Even more permissive aspect ratio
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0

            if debug:
                print(f"  fill_ratio={fill_ratio:.2f}")

            # Since we're detecting filled card regions, expect reasonable fill ratio
            if fill_ratio > 0.2:  # Very permissive fill ratio
                # Add padding to ensure complete card capture
                padding = 20
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)

                card_candidates.append({
                    'bbox': (x_padded, y_padded, w_padded, h_padded),
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'fill_ratio': fill_ratio
                })

                if debug:
                    print(f"  ✅ Added as card candidate")
            else:
                if debug:
                    print(f"  ❌ Skipped - fill ratio too low")
        else:
            if debug:
                print(f"  ❌ Skipped - aspect ratio out of range")
    
    # Sort by area (largest first) and take top 2
    card_candidates.sort(key=lambda x: x['area'], reverse=True)
    selected_cards = card_candidates[:2]
    
    # Save debug images if requested
    if debug:
        cv2.imwrite(str(output_dir / "debug_1_original_gray.jpg"), gray)
        cv2.imwrite(str(output_dir / "debug_2_very_heavy_blur.jpg"), very_heavy_blur)
        cv2.imwrite(str(output_dir / "debug_3_otsu_mask.jpg"), otsu_mask)
        cv2.imwrite(str(output_dir / "debug_4_adaptive_mask.jpg"), adaptive_mask)
        cv2.imwrite(str(output_dir / "debug_5_edges.jpg"), edges)
        cv2.imwrite(str(output_dir / "debug_6_final_mask.jpg"), card_mask)

        if selected_cards:
            visualization = image.copy()
            for i, card in enumerate(selected_cards):
                x, y, w, h = card['bbox']
                cv2.rectangle(visualization, (x, y), (x + w, y + h), (0, 255, 0), 3)
                cv2.putText(visualization, f"Complete Card {i+1}", (x, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.imwrite(str(output_dir / "debug_7_detection.jpg"), visualization)

        print(f"Found {len(contours)} contours, {len(card_candidates)} card candidates")
    
    # Crop and save cards
    output_files = []
    for i, card in enumerate(selected_cards):
        x, y, w, h = card['bbox']
        cropped_card = image[y:y+h, x:x+w]
        
        output_filename = f"complete_card_{i+1}.jpg"
        output_path = output_dir / output_filename
        cv2.imwrite(str(output_path), cropped_card)
        output_files.append(str(output_path))
        
        if debug:
            print(f"Complete Card {i+1}: bbox=({x},{y},{w},{h}), area={card['area']:.0f}, "
                  f"aspect_ratio={card['aspect_ratio']:.2f}, fill_ratio={card['fill_ratio']:.2f}")
    
    return output_files


def get_complete_card_bboxes(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """
    Get bounding boxes of complete cards from a numpy image array.
    
    Args:
        image: Input image as numpy array (BGR format)
    
    Returns:
        List of bounding boxes as (x, y, width, height) tuples
    """
    height, width = image.shape[:2]
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Use same approach as main function
    very_heavy_blur = cv2.GaussianBlur(gray, (51, 51), 0)
    
    # Find background color
    corner_size = 50
    corners = [
        very_heavy_blur[0:corner_size, 0:corner_size],
        very_heavy_blur[0:corner_size, -corner_size:],
        very_heavy_blur[-corner_size:, 0:corner_size],
        very_heavy_blur[-corner_size:, -corner_size:]
    ]
    background_values = [np.mean(corner) for corner in corners]
    background_color = np.mean(background_values)
    
    # Create card mask
    diff_from_bg = np.abs(very_heavy_blur.astype(float) - background_color)
    threshold_value = np.std(diff_from_bg) * 1.5
    _, card_mask = cv2.threshold(diff_from_bg.astype(np.uint8), int(threshold_value), 255, cv2.THRESH_BINARY)
    
    # Morphological operations
    large_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (30, 30))
    card_mask = cv2.morphologyEx(card_mask, cv2.MORPH_CLOSE, large_kernel)
    very_large_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 50))
    card_mask = cv2.morphologyEx(card_mask, cv2.MORPH_CLOSE, very_large_kernel)
    small_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (10, 10))
    card_mask = cv2.morphologyEx(card_mask, cv2.MORPH_OPEN, small_kernel)
    
    # Find contours and filter
    contours, _ = cv2.findContours(card_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    card_bboxes = []
    image_area = width * height
    min_card_area = image_area * 0.01
    max_card_area = image_area * 0.95
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < min_card_area or area > max_card_area:
            continue
        
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        
        if 0.8 <= aspect_ratio <= 5.0:
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0
            
            if fill_ratio > 0.3:
                padding = 20
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)
                
                card_bboxes.append((x_padded, y_padded, w_padded, h_padded))
    
    # Sort by area and return top 2
    card_bboxes.sort(key=lambda bbox: bbox[2] * bbox[3], reverse=True)
    return card_bboxes[:2]


# Test the function
if __name__ == "__main__":
    try:
        print("Testing COMPLETE card detection...")
        card_files = detect_complete_cards("test images/test4.jpg", "complete_test", debug=True)
        print(f"✅ Success! Detected {len(card_files)} COMPLETE cards:")
        for i, file_path in enumerate(card_files, 1):
            print(f"   Complete Card {i}: {file_path}")
        
        # Test with numpy array
        print("\nTesting with numpy array...")
        image = cv2.imread("test images/test4.jpg")
        bboxes = get_complete_card_bboxes(image)
        print(f"✅ Found {len(bboxes)} complete card bounding boxes:")
        for i, (x, y, w, h) in enumerate(bboxes, 1):
            print(f"   Complete Card {i}: position=({x},{y}), size=({w}x{h})")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you have test images in the 'test images' directory")

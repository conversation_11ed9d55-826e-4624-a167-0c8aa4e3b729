#!/usr/bin/env python3
"""
Test script to verify that the card detection system is properly installed and working.
"""

import sys
import traceback
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        import cv2
        print(f"✓ OpenCV version: {cv2.__version__}")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy version: {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False
    
    try:
        from detect_and_crop_cards import CardDetector
        print("✓ CardDetector class imported successfully")
    except ImportError as e:
        print(f"✗ CardDetector import failed: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality of the CardDetector."""
    print("\nTesting basic functionality...")
    
    try:
        from detect_and_crop_cards import CardDetector
        
        # Create detector instance
        detector = CardDetector(debug_mode=False)
        print("✓ CardDetector instance created")
        
        # Check if test images exist
        test_dir = Path("test images")
        if not test_dir.exists():
            print("✗ Test images directory not found")
            return False
        
        test_images = list(test_dir.glob("*.jpg"))
        if not test_images:
            print("✗ No test images found")
            return False
        
        print(f"✓ Found {len(test_images)} test images")
        
        # Test with one image
        test_image = test_images[0]
        print(f"Testing with: {test_image}")
        
        result = detector.detect_and_crop_cards(str(test_image), "test_output")
        
        if result['success']:
            print(f"✓ Detection successful: {result['cards_found']} cards found")
            return True
        else:
            print(f"✗ Detection failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        traceback.print_exc()
        return False

def test_command_line():
    """Test command line interface."""
    print("\nTesting command line interface...")
    
    try:
        import subprocess
        
        # Test help command
        result = subprocess.run([sys.executable, "detect_and_crop_cards.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ Command line help works")
            return True
        else:
            print(f"✗ Command line help failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Command line test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("="*60)
    print("CARD DETECTION SYSTEM - INSTALLATION TEST")
    print("="*60)
    
    all_passed = True
    
    # Test imports
    if not test_imports():
        all_passed = False
    
    # Test basic functionality
    if not test_basic_functionality():
        all_passed = False
    
    # Test command line
    if not test_command_line():
        all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! The system is ready to use.")
        print("\nNext steps:")
        print("1. Try: python detect_and_crop_cards.py 'test images/test4.jpg'")
        print("2. Or run: python example_usage.py")
    else:
        print("❌ SOME TESTS FAILED! Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Install requirements: pip install -r requirements.txt")
        print("2. Make sure test images are in the 'test images' directory")
        print("3. Check that Python and OpenCV are properly installed")
    
    print("="*60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit(main())

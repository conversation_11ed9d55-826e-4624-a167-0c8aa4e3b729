import cv2
import numpy as np
from pathlib import Path
import argparse
import json
from typing import List, Tuple, Dict, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CardDetector:
    """
    Advanced card detection and cropping system using multiple computer vision techniques.
    Designed specifically for family record cards and similar documents.
    """

    def __init__(self, debug_mode: bool = True):
        self.debug_mode = debug_mode
        self.min_card_area = 50000  # Minimum area for a valid card
        self.max_card_area = 2000000  # Maximum area for a valid card
        self.min_aspect_ratio = 1.2  # Minimum width/height ratio
        self.max_aspect_ratio = 3.5  # Maximum width/height ratio

    def preprocess_image(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Advanced preprocessing pipeline for better card detection.

        Args:
            image: Input BGR image

        Returns:
            Tuple of (gray, binary, edges) images
        """
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Apply adaptive thresholding for better edge detection
        binary = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        # Morphological operations to clean up the binary image
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

        # Edge detection with multiple methods
        edges1 = cv2.Canny(blurred, 50, 150, apertureSize=3)
        edges2 = cv2.Canny(binary, 50, 150, apertureSize=3)

        # Combine edge detection results
        edges = cv2.bitwise_or(edges1, edges2)

        # Dilate edges to connect nearby contours
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        edges = cv2.dilate(edges, kernel, iterations=1)

        return gray, binary, edges

    def find_card_contours(self, edges: np.ndarray, image_shape: Tuple[int, int]) -> List[Tuple[int, int, int, int]]:
        """
        Find potential card contours using advanced filtering.

        Args:
            edges: Edge-detected image
            image_shape: Original image shape (height, width)

        Returns:
            List of bounding rectangles (x, y, w, h) for detected cards
        """
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        logger.info(f"Found {len(contours)} total contours")

        card_candidates = []

        for i, contour in enumerate(contours):
            # Calculate contour area
            area = cv2.contourArea(contour)

            # Skip very small or very large contours
            if area < self.min_card_area or area > self.max_card_area:
                continue

            # Approximate the contour to a polygon
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)

            # Calculate aspect ratio
            aspect_ratio = w / h if h > 0 else 0

            # Filter based on aspect ratio and size
            if (self.min_aspect_ratio <= aspect_ratio <= self.max_aspect_ratio and
                w > 200 and h > 100):

                # Calculate additional metrics
                rect_area = w * h
                fill_ratio = area / rect_area if rect_area > 0 else 0

                # Check if the contour is roughly rectangular
                if len(approx) >= 4 and fill_ratio > 0.7:
                    card_candidates.append({
                        'bbox': (x, y, w, h),
                        'area': area,
                        'aspect_ratio': aspect_ratio,
                        'fill_ratio': fill_ratio,
                        'contour': contour,
                        'confidence': self._calculate_confidence(area, aspect_ratio, fill_ratio, len(approx))
                    })

                    logger.info(f"Card candidate {len(card_candidates)}: "
                              f"bbox=({x},{y},{w},{h}), area={area:.0f}, "
                              f"aspect_ratio={aspect_ratio:.2f}, fill_ratio={fill_ratio:.2f}")

        # Sort by confidence score
        card_candidates.sort(key=lambda x: x['confidence'], reverse=True)

        # Return top candidates as bounding boxes
        return [candidate['bbox'] for candidate in card_candidates[:2]]

    def _calculate_confidence(self, area: float, aspect_ratio: float, fill_ratio: float, vertices: int) -> float:
        """Calculate confidence score for a card candidate."""
        confidence = 0.0

        # Area score (prefer medium-large areas)
        if 100000 <= area <= 500000:
            confidence += 0.3
        elif area > 50000:
            confidence += 0.2

        # Aspect ratio score (prefer card-like ratios)
        if 1.4 <= aspect_ratio <= 2.5:
            confidence += 0.3
        elif 1.2 <= aspect_ratio <= 3.0:
            confidence += 0.2

        # Fill ratio score (prefer well-filled rectangles)
        if fill_ratio > 0.85:
            confidence += 0.2
        elif fill_ratio > 0.7:
            confidence += 0.1

        # Vertex score (prefer rectangular shapes)
        if vertices == 4:
            confidence += 0.2
        elif vertices <= 6:
            confidence += 0.1

        return confidence

    def detect_and_crop_cards(self, image_path: str, output_dir: str = None) -> Dict:
        """
        Main method to detect and crop cards from an image.

        Args:
            image_path: Path to the input image
            output_dir: Directory to save cropped cards (optional)

        Returns:
            Dictionary with detection results and metadata
        """
        image_path = Path(image_path)
        if output_dir is None:
            output_dir = image_path.parent
        else:
            output_dir = Path(output_dir)
            output_dir.mkdir(exist_ok=True)

        # Read the image
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"Could not read image from {image_path}")

        logger.info(f"Processing image: {image_path}")
        logger.info(f"Image dimensions: {image.shape}")

        # Preprocess the image
        gray, binary, edges = self.preprocess_image(image)

        # Save debug images if enabled
        if self.debug_mode:
            cv2.imwrite(str(output_dir / "debug_gray.jpg"), gray)
            cv2.imwrite(str(output_dir / "debug_binary.jpg"), binary)
            cv2.imwrite(str(output_dir / "debug_edges.jpg"), edges)
            logger.info(f"Debug images saved to {output_dir}")

        # Find card contours
        card_bboxes = self.find_card_contours(edges, image.shape[:2])

        if not card_bboxes:
            logger.warning("No cards detected in the image")
            return {
                'success': False,
                'message': 'No cards detected',
                'cards_found': 0,
                'output_files': []
            }

        logger.info(f"Detected {len(card_bboxes)} cards")

        # Crop and save cards
        output_files = []
        cropped_cards = []

        for i, (x, y, w, h) in enumerate(card_bboxes):
            # Extract the card region
            card_image = image[y:y+h, x:x+w]
            cropped_cards.append(card_image)

            # Save the cropped card
            output_filename = f"card_{i+1}.jpg"
            output_path = output_dir / output_filename
            cv2.imwrite(str(output_path), card_image)
            output_files.append(str(output_path))

            logger.info(f"Card {i+1} saved to: {output_path}")
            logger.info(f"Card {i+1} dimensions: {card_image.shape}")

        # Create visualization with detected cards
        if self.debug_mode:
            visualization = image.copy()
            for i, (x, y, w, h) in enumerate(card_bboxes):
                # Draw bounding rectangle
                cv2.rectangle(visualization, (x, y), (x+w, y+h), (0, 255, 0), 3)
                # Add label
                cv2.putText(visualization, f"Card {i+1}", (x, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

            viz_path = output_dir / "detection_visualization.jpg"
            cv2.imwrite(str(viz_path), visualization)
            logger.info(f"Detection visualization saved to: {viz_path}")

        return {
            'success': True,
            'message': f'Successfully detected and cropped {len(card_bboxes)} cards',
            'cards_found': len(card_bboxes),
            'output_files': output_files,
            'card_bboxes': card_bboxes,
            'input_image_shape': image.shape
        }

    def batch_process(self, input_dir: str, output_dir: str = None) -> List[Dict]:
        """
        Process multiple images in a directory.

        Args:
            input_dir: Directory containing input images
            output_dir: Directory to save results (optional)

        Returns:
            List of results for each processed image
        """
        input_dir = Path(input_dir)
        if output_dir is None:
            output_dir = input_dir / "cropped_cards"
        else:
            output_dir = Path(output_dir)

        output_dir.mkdir(exist_ok=True)

        # Supported image extensions
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

        # Find all image files
        image_files = [f for f in input_dir.iterdir()
                      if f.suffix.lower() in image_extensions]

        if not image_files:
            logger.warning(f"No image files found in {input_dir}")
            return []

        logger.info(f"Found {len(image_files)} image files to process")

        results = []
        for image_file in image_files:
            try:
                # Create subdirectory for each image
                image_output_dir = output_dir / image_file.stem
                image_output_dir.mkdir(exist_ok=True)

                # Process the image
                result = self.detect_and_crop_cards(str(image_file), str(image_output_dir))
                result['input_file'] = str(image_file)
                results.append(result)

            except Exception as e:
                logger.error(f"Error processing {image_file}: {str(e)}")
                results.append({
                    'success': False,
                    'message': f'Error: {str(e)}',
                    'input_file': str(image_file),
                    'cards_found': 0,
                    'output_files': []
                })

        return results


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Advanced card detection and cropping tool for family record cards",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process a single image
  python detect_and_crop_cards.py input.jpg

  # Process with custom output directory
  python detect_and_crop_cards.py input.jpg --output-dir ./results

  # Batch process all images in a directory
  python detect_and_crop_cards.py --batch-dir ./test_images --output-dir ./results

  # Disable debug mode for faster processing
  python detect_and_crop_cards.py input.jpg --no-debug
        """
    )

    # Input options
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('image_path', nargs='?', help='Path to input image file')
    group.add_argument('--batch-dir', help='Directory containing images to process')

    # Output options
    parser.add_argument('--output-dir', help='Output directory for cropped cards')
    parser.add_argument('--no-debug', action='store_true', help='Disable debug mode')

    # Detection parameters
    parser.add_argument('--min-area', type=int, default=50000,
                       help='Minimum area for card detection (default: 50000)')
    parser.add_argument('--max-area', type=int, default=2000000,
                       help='Maximum area for card detection (default: 2000000)')
    parser.add_argument('--min-aspect', type=float, default=1.2,
                       help='Minimum aspect ratio for cards (default: 1.2)')
    parser.add_argument('--max-aspect', type=float, default=3.5,
                       help='Maximum aspect ratio for cards (default: 3.5)')

    args = parser.parse_args()

    # Create detector with custom parameters
    detector = CardDetector(debug_mode=not args.no_debug)
    detector.min_card_area = args.min_area
    detector.max_card_area = args.max_area
    detector.min_aspect_ratio = args.min_aspect
    detector.max_aspect_ratio = args.max_aspect

    try:
        if args.batch_dir:
            # Batch processing mode
            logger.info(f"Starting batch processing of directory: {args.batch_dir}")
            results = detector.batch_process(args.batch_dir, args.output_dir)

            # Print summary
            successful = sum(1 for r in results if r['success'])
            total_cards = sum(r['cards_found'] for r in results)

            print(f"\n{'='*60}")
            print(f"BATCH PROCESSING SUMMARY")
            print(f"{'='*60}")
            print(f"Images processed: {len(results)}")
            print(f"Successful: {successful}")
            print(f"Failed: {len(results) - successful}")
            print(f"Total cards detected: {total_cards}")
            print(f"{'='*60}")

            # Save detailed results
            if args.output_dir:
                results_file = Path(args.output_dir) / "batch_results.json"
                with open(results_file, 'w') as f:
                    json.dump(results, f, indent=2)
                print(f"Detailed results saved to: {results_file}")

        else:
            # Single image processing mode
            logger.info(f"Processing single image: {args.image_path}")
            result = detector.detect_and_crop_cards(args.image_path, args.output_dir)

            # Print results
            print(f"\n{'='*60}")
            print(f"DETECTION RESULTS")
            print(f"{'='*60}")
            print(f"Success: {result['success']}")
            print(f"Message: {result['message']}")
            print(f"Cards found: {result['cards_found']}")

            if result['success'] and result['output_files']:
                print(f"\nOutput files:")
                for i, file_path in enumerate(result['output_files'], 1):
                    print(f"  Card {i}: {file_path}")

            print(f"{'='*60}")

    except Exception as e:
        logger.error(f"Error during processing: {str(e)}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
import cv2
import numpy as np
from typing import List, Tuple, Optional

def detect_and_crop_cards(image_path: str, output_dir: str = ".", debug: bool = False) -> List[str]:
    """
    Simple function to detect and crop complete family record cards from an image.

    Args:
        image_path: Path to the input image
        output_dir: Directory to save cropped cards (default: current directory)
        debug: Whether to save debug images (default: False)

    Returns:
        List of paths to the cropped card images
    """
    import os
    from pathlib import Path

    # Create output directory if it doesn't exist
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)

    # Read the image
    image = cv2.imread(str(image_path))
    if image is None:
        raise ValueError(f"Could not read image from {image_path}")

    # Get image dimensions
    height, width = image.shape[:2]

    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Use multiple edge detection approaches to find card boundaries
    # Method 1: Standard Canny with low thresholds for outer boundaries
    edges1 = cv2.Canny(blurred, 20, 60)

    # Method 2: Adaptive threshold to find card vs background contrast
    binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 10)
    edges2 = cv2.Canny(binary, 50, 150)

    # Combine both edge detection methods
    edges = cv2.bitwise_or(edges1, edges2)

    # Apply morphological operations to connect card boundary segments
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
    edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

    # Dilate to ensure card boundaries are connected
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    edges = cv2.dilate(edges, kernel, iterations=1)

    if debug:
        cv2.imwrite(str(output_dir / "debug_edges1.jpg"), edges1)
        cv2.imwrite(str(output_dir / "debug_edges2.jpg"), edges2)
        cv2.imwrite(str(output_dir / "debug_edges_combined.jpg"), edges)
        cv2.imwrite(str(output_dir / "debug_binary.jpg"), binary)

    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if debug:
        print(f"Found {len(contours)} contours")

    # Filter contours to find card-like shapes
    card_candidates = []

    # More flexible area thresholds - cards can vary in size
    min_area = 50000  # Minimum area for a card
    max_area = (width * height) * 0.95  # Almost the entire image

    for i, contour in enumerate(contours):
        # Calculate contour area
        area = cv2.contourArea(contour)

        if debug:
            print(f"Contour {i}: area={area}")

        # Skip contours that are too small or too large
        if area < min_area or area > max_area:
            continue

        # Get bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)

        # Calculate aspect ratio
        aspect_ratio = w / h if h > 0 else 0

        if debug:
            print(f"Contour {i}: bbox=({x},{y},{w},{h}), aspect_ratio={aspect_ratio:.2f}")

        # Filter for card-like aspect ratios (family cards are typically rectangular)
        if 1.1 <= aspect_ratio <= 4.0:  # More flexible aspect ratio
            # Calculate how much of the bounding rectangle is filled by the contour
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0

            if debug:
                print(f"Contour {i}: fill_ratio={fill_ratio:.2f}")

            # Cards should fill a reasonable portion of their bounding rectangle
            if fill_ratio > 0.3:  # More flexible fill ratio
                # Add padding to ensure we get the complete card
                padding = 20  # Increased padding
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)

                card_candidates.append({
                    'bbox': (x_padded, y_padded, w_padded, h_padded),
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'fill_ratio': fill_ratio
                })

                if debug:
                    print(f"Added card candidate: bbox=({x_padded},{y_padded},{w_padded},{h_padded})")

    # Sort by area (largest first) to get the most prominent cards
    card_candidates.sort(key=lambda x: x['area'], reverse=True)

    # Take up to 2 largest cards
    selected_cards = card_candidates[:2]

    if debug:
        print(f"Selected {len(selected_cards)} cards")

    # Crop and save the cards
    output_files = []

    if debug:
        # Create visualization
        visualization = image.copy()
        for i, card in enumerate(selected_cards):
            x, y, w, h = card['bbox']
            cv2.rectangle(visualization, (x, y), (x + w, y + h), (0, 255, 0), 3)
            cv2.putText(visualization, f"Card {i+1}", (x, y-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.imwrite(str(output_dir / "debug_detection.jpg"), visualization)

    for i, card in enumerate(selected_cards):
        x, y, w, h = card['bbox']

        # Crop the card
        cropped_card = image[y:y+h, x:x+w]

        # Save the cropped card
        output_filename = f"card_{i+1}.jpg"
        output_path = output_dir / output_filename
        cv2.imwrite(str(output_path), cropped_card)
        output_files.append(str(output_path))

        if debug:
            print(f"Card {i+1}: bbox=({x},{y},{w},{h}), area={card['area']:.0f}, "
                  f"aspect_ratio={card['aspect_ratio']:.2f}, fill_ratio={card['fill_ratio']:.2f}")

    return output_files


def detect_cards_from_image(image: np.ndarray, padding: int = 10) -> List[Tuple[int, int, int, int]]:
    """
    Detect card bounding boxes from a numpy image array.
    
    Args:
        image: Input image as numpy array (BGR format)
        padding: Padding to add around detected cards (default: 10)
    
    Returns:
        List of bounding boxes as (x, y, width, height) tuples
    """
    height, width = image.shape[:2]
    
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Apply Gaussian blur
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # Adaptive threshold
    binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    
    # Morphological operations
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    # Edge detection
    edges = cv2.Canny(blurred, 30, 100)
    
    # Dilate edges
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    edges = cv2.dilate(edges, kernel, iterations=2)
    
    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours
    card_bboxes = []
    min_area = (width * height) * 0.1
    max_area = (width * height) * 0.9
    
    for contour in contours:
        area = cv2.contourArea(contour)
        
        if area < min_area or area > max_area:
            continue
        
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        
        if 1.2 <= aspect_ratio <= 3.5:
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0
            
            if fill_ratio > 0.6:
                # Add padding
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)
                
                card_bboxes.append((x_padded, y_padded, w_padded, h_padded))
    
    # Sort by area and return top 2
    card_bboxes.sort(key=lambda bbox: bbox[2] * bbox[3], reverse=True)
    return card_bboxes[:2]


# Example usage
if __name__ == "__main__":
    # Simple usage example
    image_path = "test images/test4.jpg"
    output_files = detect_and_crop_cards(image_path, output_dir="simple_output", debug=True)
    
    print(f"Detected and cropped {len(output_files)} cards:")
    for i, file_path in enumerate(output_files, 1):
        print(f"  Card {i}: {file_path}")

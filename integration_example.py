"""
Integration Example: How to use the simple card detector in your project
"""

from card_detector_simple import detect_and_crop_cards, get_card_bounding_boxes
import cv2

def example_1_basic_usage():
    """Example 1: Basic usage - detect and save cards"""
    print("=== Example 1: Basic Usage ===")
    
    # Simple one-line usage
    card_files = detect_and_crop_cards("test images/test4.jpg")
    
    print(f"Detected {len(card_files)} cards:")
    for i, file_path in enumerate(card_files, 1):
        print(f"  Card {i}: {file_path}")


def example_2_custom_output():
    """Example 2: Custom output directory"""
    print("\n=== Example 2: Custom Output Directory ===")
    
    # Specify custom output directory
    card_files = detect_and_crop_cards(
        image_path="test images/test4.jpg",
        output_dir="my_cards",
        debug=True  # Save debug images to see detection process
    )
    
    print(f"Cards saved to custom directory:")
    for file_path in card_files:
        print(f"  {file_path}")


def example_3_process_multiple_images():
    """Example 3: Process multiple images"""
    print("\n=== Example 3: Process Multiple Images ===")
    
    image_files = [
        "test images/test1.jpg",
        "test images/test4.jpg",
        "test images/test5.jpg"
    ]
    
    all_cards = []
    for image_file in image_files:
        try:
            card_files = detect_and_crop_cards(image_file, f"output_{image_file.split('/')[-1].split('.')[0]}")
            all_cards.extend(card_files)
            print(f"  {image_file}: {len(card_files)} cards detected")
        except Exception as e:
            print(f"  {image_file}: Error - {e}")
    
    print(f"Total cards detected: {len(all_cards)}")


def example_4_get_bounding_boxes():
    """Example 4: Get bounding boxes for further processing"""
    print("\n=== Example 4: Get Bounding Boxes ===")
    
    # Load image
    image = cv2.imread("test images/test4.jpg")
    
    # Get bounding boxes
    bboxes = get_card_bounding_boxes(image)
    
    print(f"Found {len(bboxes)} cards:")
    for i, (x, y, w, h) in enumerate(bboxes, 1):
        print(f"  Card {i}: x={x}, y={y}, width={w}, height={h}")
        
        # You can now do custom processing with each card
        card_image = image[y:y+h, x:x+w]
        
        # Example: Save with custom naming
        cv2.imwrite(f"custom_card_{i}.jpg", card_image)
        
        # Example: Get card dimensions
        card_height, card_width = card_image.shape[:2]
        print(f"    Card {i} dimensions: {card_width}x{card_height} pixels")


def example_5_error_handling():
    """Example 5: Proper error handling"""
    print("\n=== Example 5: Error Handling ===")
    
    try:
        # Try to process a non-existent file
        card_files = detect_and_crop_cards("non_existent_image.jpg")
    except ValueError as e:
        print(f"  Expected error caught: {e}")
    
    try:
        # Process a valid image
        card_files = detect_and_crop_cards("test images/test1.jpg")
        print(f"  Successfully processed: {len(card_files)} cards")
    except Exception as e:
        print(f"  Unexpected error: {e}")


def example_6_integration_in_your_class():
    """Example 6: How to integrate in your own class"""
    print("\n=== Example 6: Integration in Your Class ===")
    
    class MyCardProcessor:
        def __init__(self, output_base_dir="processed_cards"):
            self.output_base_dir = output_base_dir
        
        def process_family_cards(self, image_path, family_name):
            """Process family cards and organize by family name"""
            import os
            
            # Create family-specific directory
            family_dir = os.path.join(self.output_base_dir, family_name)
            
            # Detect and crop cards
            card_files = detect_and_crop_cards(image_path, family_dir)
            
            # Return results with metadata
            return {
                'family_name': family_name,
                'image_path': image_path,
                'cards_detected': len(card_files),
                'card_files': card_files,
                'output_directory': family_dir
            }
        
        def batch_process_families(self, family_images):
            """Process multiple family images"""
            results = []
            for family_name, image_path in family_images.items():
                try:
                    result = self.process_family_cards(image_path, family_name)
                    results.append(result)
                    print(f"  ✅ {family_name}: {result['cards_detected']} cards")
                except Exception as e:
                    print(f"  ❌ {family_name}: Error - {e}")
                    results.append({
                        'family_name': family_name,
                        'error': str(e),
                        'cards_detected': 0
                    })
            return results
    
    # Example usage of the class
    processor = MyCardProcessor()
    
    # Process single family
    result = processor.process_family_cards("test images/test4.jpg", "Al_Qarni_Family")
    print(f"  Processed {result['family_name']}: {result['cards_detected']} cards")
    
    # Process multiple families
    family_images = {
        "Family_A": "test images/test1.jpg",
        "Family_B": "test images/test4.jpg",
        "Family_C": "test images/test5.jpg"
    }
    
    results = processor.batch_process_families(family_images)
    total_cards = sum(r.get('cards_detected', 0) for r in results)
    print(f"  Total cards processed: {total_cards}")


if __name__ == "__main__":
    print("🎯 Card Detector Integration Examples")
    print("=" * 50)
    
    # Run all examples
    example_1_basic_usage()
    example_2_custom_output()
    example_3_process_multiple_images()
    example_4_get_bounding_boxes()
    example_5_error_handling()
    example_6_integration_in_your_class()
    
    print("\n" + "=" * 50)
    print("✅ All examples completed!")
    print("\n💡 Key Points for Integration:")
    print("1. Import: from card_detector_simple import detect_and_crop_cards")
    print("2. Basic usage: card_files = detect_and_crop_cards('image.jpg')")
    print("3. The function returns a list of file paths to cropped cards")
    print("4. Cards are cropped with the COMPLETE boundaries (not just inner table)")
    print("5. Use debug=True to see the detection process")
    print("6. The function handles errors gracefully with clear error messages")

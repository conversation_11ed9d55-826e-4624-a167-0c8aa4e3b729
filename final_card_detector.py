import cv2
import numpy as np
from typing import List, Tuple
from pathlib import Path

def detect_complete_family_cards(image_path: str, output_dir: str = ".", debug: bool = False) -> List[str]:
    """
    Final solution: Detect and crop COMPLETE family record cards including headers and borders.
    
    This function uses a robust multi-approach strategy to find the outer boundaries of cards,
    not just the inner table structures.
    
    Args:
        image_path: Path to the input image file
        output_dir: Directory to save cropped cards (default: current directory)
        debug: Whether to save debug images (default: False)
    
    Returns:
        List of file paths to the cropped card images
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Read the image
    image = cv2.imread(str(image_path))
    if image is None:
        raise ValueError(f"Could not read image from {image_path}")
    
    height, width = image.shape[:2]
    
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # ROBUST MULTI-APPROACH STRATEGY
    
    # Approach 1: Detect card boundaries using gentle edge detection
    # Apply moderate blur to reduce internal details while preserving card edges
    moderate_blur = cv2.GaussianBlur(gray, (11, 11), 0)
    
    # Use very low Canny thresholds to detect subtle card boundaries
    card_edges = cv2.Canny(moderate_blur, 10, 30)
    
    # Approach 2: Use morphological operations to find rectangular structures
    # Create binary image using adaptive threshold
    binary = cv2.adaptiveThreshold(moderate_blur, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 21, 10)
    
    # Use large rectangular kernel to connect card boundaries
    rect_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 25))
    morphed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, rect_kernel)
    
    # Approach 3: Combine edge and morphological approaches
    combined = cv2.bitwise_or(card_edges, morphed)
    
    # Apply final morphological operations to create clean card regions
    final_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 15))
    final_mask = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, final_kernel)
    
    # Remove small noise
    cleanup_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, cleanup_kernel)
    
    # Find contours
    contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours to find card-like shapes
    card_candidates = []
    
    # Calculate area thresholds
    image_area = width * height
    min_card_area = 30000  # Absolute minimum area for a card
    max_card_area = image_area * 0.7  # Maximum 70% of image
    
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        
        if debug:
            print(f"Contour {i}: area={area:.0f}")
        
        if area < min_card_area or area > max_card_area:
            if debug:
                print(f"  Skipped - area out of range (min: {min_card_area}, max: {max_card_area:.0f})")
            continue
        
        # Get bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        
        if debug:
            print(f"  bbox=({x},{y},{w},{h}), aspect_ratio={aspect_ratio:.2f}")
        
        # Filter for card-like aspect ratios and sizes
        if (1.0 <= aspect_ratio <= 4.0 and  # Reasonable aspect ratio
            w > 200 and h > 150):  # Minimum dimensions
            
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0
            
            if debug:
                print(f"  fill_ratio={fill_ratio:.2f}")
            
            # Accept contours with reasonable fill ratios
            if fill_ratio > 0.2:  # Very permissive
                # Add generous padding to ensure complete card capture
                padding = 30
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)
                
                card_candidates.append({
                    'bbox': (x_padded, y_padded, w_padded, h_padded),
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'fill_ratio': fill_ratio
                })
                
                if debug:
                    print(f"  ✅ Added as card candidate")
            else:
                if debug:
                    print(f"  ❌ Skipped - fill ratio too low")
        else:
            if debug:
                print(f"  ❌ Skipped - aspect ratio or size out of range")
    
    # Sort by area (largest first) and take top 2
    card_candidates.sort(key=lambda x: x['area'], reverse=True)
    selected_cards = card_candidates[:2]
    
    # Save debug images if requested
    if debug:
        cv2.imwrite(str(output_dir / "debug_1_original_gray.jpg"), gray)
        cv2.imwrite(str(output_dir / "debug_2_moderate_blur.jpg"), moderate_blur)
        cv2.imwrite(str(output_dir / "debug_3_card_edges.jpg"), card_edges)
        cv2.imwrite(str(output_dir / "debug_4_binary.jpg"), binary)
        cv2.imwrite(str(output_dir / "debug_5_morphed.jpg"), morphed)
        cv2.imwrite(str(output_dir / "debug_6_combined.jpg"), combined)
        cv2.imwrite(str(output_dir / "debug_7_final_mask.jpg"), final_mask)
        
        if selected_cards:
            visualization = image.copy()
            for i, card in enumerate(selected_cards):
                x, y, w, h = card['bbox']
                cv2.rectangle(visualization, (x, y), (x + w, y + h), (0, 255, 0), 4)
                cv2.putText(visualization, f"COMPLETE CARD {i+1}", (x, y-15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 0), 3)
            cv2.imwrite(str(output_dir / "debug_8_detection.jpg"), visualization)
        
        print(f"Found {len(contours)} contours, {len(card_candidates)} card candidates, {len(selected_cards)} selected")
    
    # Crop and save cards
    output_files = []
    for i, card in enumerate(selected_cards):
        x, y, w, h = card['bbox']
        cropped_card = image[y:y+h, x:x+w]
        
        output_filename = f"complete_family_card_{i+1}.jpg"
        output_path = output_dir / output_filename
        cv2.imwrite(str(output_path), cropped_card)
        output_files.append(str(output_path))
        
        if debug:
            print(f"COMPLETE CARD {i+1}: bbox=({x},{y},{w},{h}), area={card['area']:.0f}, "
                  f"aspect_ratio={card['aspect_ratio']:.2f}, fill_ratio={card['fill_ratio']:.2f}")
    
    return output_files


def get_complete_family_card_bboxes(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """
    Get bounding boxes of complete family cards from a numpy image array.
    
    Args:
        image: Input image as numpy array (BGR format)
    
    Returns:
        List of bounding boxes as (x, y, width, height) tuples
    """
    height, width = image.shape[:2]
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Use same approach as main function
    moderate_blur = cv2.GaussianBlur(gray, (11, 11), 0)
    card_edges = cv2.Canny(moderate_blur, 10, 30)
    binary = cv2.adaptiveThreshold(moderate_blur, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 21, 10)
    
    rect_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 25))
    morphed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, rect_kernel)
    combined = cv2.bitwise_or(card_edges, morphed)
    
    final_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 15))
    final_mask = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, final_kernel)
    cleanup_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, cleanup_kernel)
    
    contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    card_bboxes = []
    image_area = width * height
    min_card_area = 30000
    max_card_area = image_area * 0.7
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < min_card_area or area > max_card_area:
            continue
        
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        
        if (1.0 <= aspect_ratio <= 4.0 and w > 200 and h > 150):
            rect_area = w * h
            fill_ratio = area / rect_area if rect_area > 0 else 0
            
            if fill_ratio > 0.2:
                padding = 30
                x_padded = max(0, x - padding)
                y_padded = max(0, y - padding)
                w_padded = min(width - x_padded, w + 2 * padding)
                h_padded = min(height - y_padded, h + 2 * padding)
                
                card_bboxes.append((x_padded, y_padded, w_padded, h_padded))
    
    # Sort by area and return top 2
    card_bboxes.sort(key=lambda bbox: bbox[2] * bbox[3], reverse=True)
    return card_bboxes[:2]


# Test the function
if __name__ == "__main__":
    try:
        print("🎯 Testing FINAL COMPLETE family card detection...")
        card_files = detect_complete_family_cards("test images/test4.jpg", "final_test", debug=True)
        print(f"✅ SUCCESS! Detected {len(card_files)} COMPLETE family cards:")
        for i, file_path in enumerate(card_files, 1):
            print(f"   📄 Complete Family Card {i}: {file_path}")
        
        # Test with numpy array
        print("\n🔍 Testing with numpy array...")
        image = cv2.imread("test images/test4.jpg")
        bboxes = get_complete_family_card_bboxes(image)
        print(f"✅ Found {len(bboxes)} complete family card bounding boxes:")
        for i, (x, y, w, h) in enumerate(bboxes, 1):
            print(f"   📐 Complete Family Card {i}: position=({x},{y}), size=({w}x{h})")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you have test images in the 'test images' directory")

import cv2
from pathlib import Path

def detect_and_crop_cards(image_path, output_paths):
    # Read the image
    image = cv2.imread(str(image_path))
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.G<PERSON><PERSON><PERSON>lur(gray, (5, 5), 0)
    edges = cv2.Canny(blurred, 20, 100)

    # Save edges debug image
    edges_path = Path(image_path).parent.parent / "edges_detected.jpg"
    cv2.imwrite(str(edges_path), edges)
    print(f"Edge detection saved as {edges_path}")

    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"Total contours found: {len(contours)}")

    # Initialize card detection
    card_contours = []

    for i, contour in enumerate(contours):
        # Approximate the contour
        approx = cv2.approxPolyDP(contour, 0.02 * cv2.arcLength(contour, True), True)
        if len(approx) == 4:  # Rectangle shapes
            x, y, w, h = cv2.boundingRect(approx)
            aspect_ratio = w / h
            print(f"Contour {i}: x={x}, y={y}, w={w}, h={h}, aspect_ratio={aspect_ratio}")

            # Relaxed conditions for better matching
            if 1.1 < aspect_ratio < 4.0 and w > 100 and h > 80:  # Adjusted filters
                card_contours.append((x, y, w, h))
                
                # Save each contour as a debug image
                cropped_card = image[y:y+h, x:x+w]
                debug_path = Path(image_path).parent.parent / f"debug_card_{i}.jpg"
                cv2.imwrite(str(debug_path), cropped_card)
                print(f"Saved debug card {i}: {debug_path}")

    # Sort by area (largest rectangles first)
    card_contours = sorted(card_contours, key=lambda item: item[2] * item[3], reverse=True)

    # Crop and save only the largest two cards
    for i, (x, y, w, h) in enumerate(card_contours[:2]):
        cropped_card = image[y:y+h, x:x+w]
        cv2.imwrite(str(output_paths[i]), cropped_card)
        print(f"Card {i+1} cropped and saved to {output_paths[i]}")

    # Save a debug image with detected card contours
    contours_debug_path = Path(image_path).parent.parent / "contours_debug.jpg"
    for x, y, w, h in card_contours:
        cv2.rectangle(image, (x, y), (x+w, y+h), (0, 255, 0), 2)  # Draw green rectangles
    cv2.imwrite(str(contours_debug_path), image)
    print(f"Contours debug image saved as {contours_debug_path}")

if __name__ == "__main__":
    script_dir = Path(__file__).parent
    image_path = script_dir / "test images" / "test4.jpg"  # Input file
    output_path_1 = script_dir / "card1.jpg"       # Cropped card 1
    output_path_2 = script_dir / "card2.jpg"       # Cropped card 2

    detect_and_crop_cards(image_path, [output_path_1, output_path_2])
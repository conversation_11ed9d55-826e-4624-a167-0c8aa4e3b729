import cv2
import numpy as np
from typing import List, Tuple
from pathlib import Path

def detect_card_rectangles(image_path: str, output_dir: str = ".", debug: bool = False) -> List[str]:
    """
    Simple approach: Find rectangular card shapes by detecting the largest rectangles in the image.
    
    This approach focuses on finding rectangular contours that could be complete cards,
    rather than trying to separate cards from background.
    
    Args:
        image_path: Path to the input image file
        output_dir: Directory to save cropped cards (default: current directory)
        debug: Whether to save debug images (default: False)
    
    Returns:
        List of file paths to the cropped card images
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Read the image
    image = cv2.imread(str(image_path))
    if image is None:
        raise ValueError(f"Could not read image from {image_path}")
    
    height, width = image.shape[:2]
    
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # SIMPLE RECTANGLE DETECTION APPROACH
    
    # Step 1: Apply light blur to reduce noise but preserve edges
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # Step 2: Use edge detection with moderate thresholds
    edges = cv2.Canny(blurred, 50, 150)
    
    # Step 3: Dilate edges to connect broken lines
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    dilated = cv2.dilate(edges, kernel, iterations=2)
    
    # Step 4: Find contours
    contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Step 5: Look for rectangular contours
    card_candidates = []
    
    for i, contour in enumerate(contours):
        # Approximate contour to polygon
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # Calculate area
        area = cv2.contourArea(contour)
        
        if debug:
            print(f"Contour {i}: area={area:.0f}, vertices={len(approx)}")
        
        # Look for contours with 4 vertices (rectangles) or close to rectangular
        if len(approx) >= 4 and area > 50000:  # Minimum area threshold
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            if debug:
                print(f"  bbox=({x},{y},{w},{h}), aspect_ratio={aspect_ratio:.2f}")
            
            # Filter for card-like shapes
            if (1.0 <= aspect_ratio <= 4.0 and  # Reasonable aspect ratio
                w > 300 and h > 200):  # Minimum size
                
                rect_area = w * h
                fill_ratio = area / rect_area if rect_area > 0 else 0
                
                if debug:
                    print(f"  fill_ratio={fill_ratio:.2f}")
                
                # Accept if it's reasonably rectangular
                if fill_ratio > 0.3:
                    # Add padding
                    padding = 25
                    x_padded = max(0, x - padding)
                    y_padded = max(0, y - padding)
                    w_padded = min(width - x_padded, w + 2 * padding)
                    h_padded = min(height - y_padded, h + 2 * padding)
                    
                    card_candidates.append({
                        'bbox': (x_padded, y_padded, w_padded, h_padded),
                        'area': area,
                        'aspect_ratio': aspect_ratio,
                        'fill_ratio': fill_ratio,
                        'vertices': len(approx)
                    })
                    
                    if debug:
                        print(f"  ✅ Added as card candidate")
                else:
                    if debug:
                        print(f"  ❌ Skipped - fill ratio too low")
            else:
                if debug:
                    print(f"  ❌ Skipped - aspect ratio or size out of range")
        else:
            if debug and area > 10000:  # Only show debug for larger contours
                print(f"  ❌ Skipped - not rectangular enough or too small")
    
    # Sort by area (largest first) and take top 2
    card_candidates.sort(key=lambda x: x['area'], reverse=True)
    selected_cards = card_candidates[:2]
    
    # Save debug images if requested
    if debug:
        cv2.imwrite(str(output_dir / "debug_1_original_gray.jpg"), gray)
        cv2.imwrite(str(output_dir / "debug_2_blurred.jpg"), blurred)
        cv2.imwrite(str(output_dir / "debug_3_edges.jpg"), edges)
        cv2.imwrite(str(output_dir / "debug_4_dilated.jpg"), dilated)
        
        # Draw all contours for debugging
        all_contours_img = image.copy()
        cv2.drawContours(all_contours_img, contours, -1, (0, 255, 255), 2)
        cv2.imwrite(str(output_dir / "debug_5_all_contours.jpg"), all_contours_img)
        
        if selected_cards:
            visualization = image.copy()
            for i, card in enumerate(selected_cards):
                x, y, w, h = card['bbox']
                cv2.rectangle(visualization, (x, y), (x + w, y + h), (0, 255, 0), 4)
                cv2.putText(visualization, f"CARD {i+1}", (x, y-15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 0), 3)
            cv2.imwrite(str(output_dir / "debug_6_detection.jpg"), visualization)
        
        print(f"Found {len(contours)} contours, {len(card_candidates)} card candidates, {len(selected_cards)} selected")
    
    # Crop and save cards
    output_files = []
    for i, card in enumerate(selected_cards):
        x, y, w, h = card['bbox']
        cropped_card = image[y:y+h, x:x+w]
        
        output_filename = f"rectangle_card_{i+1}.jpg"
        output_path = output_dir / output_filename
        cv2.imwrite(str(output_path), cropped_card)
        output_files.append(str(output_path))
        
        if debug:
            print(f"CARD {i+1}: bbox=({x},{y},{w},{h}), area={card['area']:.0f}, "
                  f"aspect_ratio={card['aspect_ratio']:.2f}, fill_ratio={card['fill_ratio']:.2f}, "
                  f"vertices={card['vertices']}")
    
    return output_files


def get_card_rectangle_bboxes(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """
    Get bounding boxes of rectangular cards from a numpy image array.
    
    Args:
        image: Input image as numpy array (BGR format)
    
    Returns:
        List of bounding boxes as (x, y, width, height) tuples
    """
    height, width = image.shape[:2]
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Use same approach as main function
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edges = cv2.Canny(blurred, 50, 150)
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    dilated = cv2.dilate(edges, kernel, iterations=2)
    
    contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    card_bboxes = []
    
    for contour in contours:
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        area = cv2.contourArea(contour)
        
        if len(approx) >= 4 and area > 50000:
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            if (1.0 <= aspect_ratio <= 4.0 and w > 300 and h > 200):
                rect_area = w * h
                fill_ratio = area / rect_area if rect_area > 0 else 0
                
                if fill_ratio > 0.3:
                    padding = 25
                    x_padded = max(0, x - padding)
                    y_padded = max(0, y - padding)
                    w_padded = min(width - x_padded, w + 2 * padding)
                    h_padded = min(height - y_padded, h + 2 * padding)
                    
                    card_bboxes.append((x_padded, y_padded, w_padded, h_padded))
    
    # Sort by area and return top 2
    card_bboxes.sort(key=lambda bbox: bbox[2] * bbox[3], reverse=True)
    return card_bboxes[:2]


# Test the function
if __name__ == "__main__":
    try:
        print("🔍 Testing SIMPLE RECTANGLE card detection...")
        card_files = detect_card_rectangles("test images/test4.jpg", "rectangle_test", debug=True)
        print(f"✅ SUCCESS! Detected {len(card_files)} rectangular cards:")
        for i, file_path in enumerate(card_files, 1):
            print(f"   📄 Rectangle Card {i}: {file_path}")
        
        # Test with numpy array
        print("\n🔍 Testing with numpy array...")
        image = cv2.imread("test images/test4.jpg")
        bboxes = get_card_rectangle_bboxes(image)
        print(f"✅ Found {len(bboxes)} rectangular card bounding boxes:")
        for i, (x, y, w, h) in enumerate(bboxes, 1):
            print(f"   📐 Rectangle Card {i}: position=({x},{y}), size=({w}x{h})")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you have test images in the 'test images' directory")

#!/usr/bin/env python3
"""
Example usage of the CardDetector class for family record card detection and cropping.
This script demonstrates different ways to use the card detection system.
"""

from detect_and_crop_cards import CardDetector
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_single_image():
    """Example: Process a single image."""
    print("="*60)
    print("EXAMPLE 1: Single Image Processing")
    print("="*60)
    
    # Create detector instance
    detector = CardDetector(debug_mode=True)
    
    # Process a single image
    image_path = "test images/test4.jpg"
    output_dir = "example_output"
    
    try:
        result = detector.detect_and_crop_cards(image_path, output_dir)
        
        print(f"Success: {result['success']}")
        print(f"Cards found: {result['cards_found']}")
        print(f"Output files: {result['output_files']}")
        
    except Exception as e:
        print(f"Error: {e}")

def example_batch_processing():
    """Example: Process multiple images in a directory."""
    print("\n" + "="*60)
    print("EXAMPLE 2: Batch Processing")
    print("="*60)
    
    # Create detector instance with custom parameters
    detector = CardDetector(debug_mode=False)  # Disable debug for faster processing
    detector.min_card_area = 30000  # Lower threshold for smaller cards
    detector.max_card_area = 3000000  # Higher threshold for larger cards
    
    # Process all images in a directory
    input_dir = "test images"
    output_dir = "batch_example_output"
    
    try:
        results = detector.batch_process(input_dir, output_dir)
        
        # Print summary
        successful = sum(1 for r in results if r['success'])
        total_cards = sum(r['cards_found'] for r in results)
        
        print(f"Images processed: {len(results)}")
        print(f"Successful: {successful}")
        print(f"Total cards detected: {total_cards}")
        
        # Print details for each image
        for result in results:
            filename = Path(result['input_file']).name
            print(f"  {filename}: {result['cards_found']} cards")
            
    except Exception as e:
        print(f"Error: {e}")

def example_custom_parameters():
    """Example: Using custom detection parameters."""
    print("\n" + "="*60)
    print("EXAMPLE 3: Custom Parameters")
    print("="*60)
    
    # Create detector with custom parameters for specific card types
    detector = CardDetector(debug_mode=True)
    
    # Adjust parameters for smaller cards
    detector.min_card_area = 20000      # Smaller minimum area
    detector.max_card_area = 1000000    # Smaller maximum area
    detector.min_aspect_ratio = 1.0     # More square cards allowed
    detector.max_aspect_ratio = 4.0     # Wider cards allowed
    
    print(f"Custom parameters:")
    print(f"  Min area: {detector.min_card_area}")
    print(f"  Max area: {detector.max_card_area}")
    print(f"  Aspect ratio range: {detector.min_aspect_ratio} - {detector.max_aspect_ratio}")
    
    # Process with custom parameters
    try:
        result = detector.detect_and_crop_cards("test images/test5.jpg", "custom_output")
        print(f"Cards detected with custom parameters: {result['cards_found']}")
        
    except Exception as e:
        print(f"Error: {e}")

def example_programmatic_access():
    """Example: Accessing detection results programmatically."""
    print("\n" + "="*60)
    print("EXAMPLE 4: Programmatic Access")
    print("="*60)
    
    detector = CardDetector(debug_mode=False)
    
    try:
        result = detector.detect_and_crop_cards("test images/test4.jpg", "programmatic_output")
        
        if result['success']:
            print(f"Detection successful!")
            print(f"Input image shape: {result['input_image_shape']}")
            print(f"Card bounding boxes: {result['card_bboxes']}")
            
            # You can now use the bounding boxes for further processing
            for i, (x, y, w, h) in enumerate(result['card_bboxes']):
                print(f"Card {i+1}: position=({x},{y}), size=({w}x{h})")
                
                # Calculate card area and aspect ratio
                area = w * h
                aspect_ratio = w / h
                print(f"  Area: {area:,} pixels")
                print(f"  Aspect ratio: {aspect_ratio:.2f}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("Family Record Card Detection - Usage Examples")
    print("This script demonstrates various ways to use the CardDetector class.")
    
    # Run all examples
    example_single_image()
    example_batch_processing()
    example_custom_parameters()
    example_programmatic_access()
    
    print("\n" + "="*60)
    print("All examples completed!")
    print("Check the output directories for results.")
    print("="*60)
